
import { ActionFunctionArgs, LoaderFunctionArgs, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useFetcher, useLoaderData, useNavigation, useOutletContext, useParams } from "@remix-run/react"; import RouteLayout from "~/layouts/RouteLayout";
import apiClient from "~/services/api.server/apiClient"; import { AuthenticationError } from "~/services/api.server/errors";
import { getSession } from "~/services/sessions.server"; import { ActionResponse, ActionResponseParams, ForbiddenResponse, JsonResponse, JsonResponseResult } from "~/utils/response";
import tryCatch from "~/utils/tryCatch";
import { getTicketById } from "./server.get-ticket";
import BackButton from "~/components/BackButton";
import { useEffect, useRef, useState } from "react";
import DeveloperListModal from "./DeveloperListModal";
import { BasicUserInfo } from "~/services/api.server/types";
import { useEditMode } from "~/utils/editMode";
import { EditModeForm } from "~/components/EditModeForm";
import { validateRole } from "~/utils/validate";
import permissions from "~/data/permissions";
import updateTicket from "./server.update-ticket";
import ChatBox from "./ChatBox";
import TicketTimeline from "./TicketTimeline";
import ArchiveWarningModal from "~/components/ArchiveWarningModal";

import AttachmentSection from "./AttachmentSection";
export const handle = {
    breadcrumb: (match: any) => {
        const ticketId = match.params.ticketId; const ticketName = match.data?.data?.name || "Ticket Details";
        return <Link to={`/tickets/mytickets/${ticketId}`}>{ticketName}</Link>;
    },
};
export async function loader({ request, params }: LoaderFunctionArgs) {
    const session = await getSession(request);
    const { ticketId } = params;
    const { data: tokenResponse,
        error: tokenError } = await tryCatch(apiClient.auth.getValidToken(session));
    if (tokenError) {
        return redirect("/logout");
    }
    try {
        const response = await getTicketById(ticketId!, tokenResponse.token);
        return JsonResponse({
            data: response,
            error: null, headers: tokenResponse.headers
        });
    } catch (error: any) {
        if (error instanceof AuthenticationError) {
            return redirect("/logout");
        }
        return JsonResponse({
            data: null,
            error: error.message, headers: tokenResponse.headers
        });
    }
}

export default function TicketDetailsRoute() {
    const { data: ticket, error } = useLoaderData<JsonResponseResult<any>>();
    const { ticketId } = useParams();
    const actionData = useActionData() as ActionResponseParams
    const { isEditing, formError, toggleEditMode } = useEditMode({ actionData });
    const { userInfo } = useOutletContext<any>();

    const navigation = useNavigation()

    // State for archive warning modal
    const [showArchiveWarning, setShowArchiveWarning] = useState(false);

    const updatePriorityFetcher = useFetcher({ key: "update-priority" })
    const updateStatusFetcher = useFetcher({ key: "update-status" })
    const updateTypeFetcher = useFetcher({ key: "update-type" })

    const getDevelopersFetcher = useFetcher({ key: "get-devs" })
    const assignDeveloperFetcher = useFetcher({ key: "update-dev" })

    const commentsFormRef = useRef<HTMLFormElement>(null);

    const deleteCommentFetcher = useFetcher({ key: "delete-comment" })
    const updateCommentFetcher = useFetcher({ key: "update-comment" })
    const getCommentsFetcher = useFetcher({ key: "get-comments" })
    const getHistoryFetcher = useFetcher({ key: "get-history" })

    useEffect(() => {
        getCommentsFetcher.load(`/tickets/${ticketId}/get-comments`)
        getHistoryFetcher.load(`/tickets/${ticketId}/get-history`)
    }, [])
    useEffect(() => {
        if (getCommentsFetcher.state === "idle" && getCommentsFetcher.data) {
            commentsFormRef.current?.reset();
        }
    }, [getCommentsFetcher.state, getCommentsFetcher.data])

    const developersModalRef = useRef<HTMLDialogElement>(null)

    const handleOnGetDevelopers = () => {
        developersModalRef?.current?.showModal();
        getDevelopersFetcher.load(`/tickets/${ticketId}/get-dev-list`)
    }

    const handleOnUnassignDeveloper = () => {
        const formData = new FormData()
        assignDeveloperFetcher.submit(formData, {
            method: "post",
            action: `/tickets/${ticketId}/update-dev`
        })
    }

    const handleOnDeleteComment = (commentId: string) => {
        const formData = new FormData()
        formData.set("commentId", commentId)
        deleteCommentFetcher.submit(formData, {
            method: "post",
            action: `/tickets/${ticketId}/delete-comment`
        })
    }

    const handleOnEditComment = (commentId: string, message: string) => {
        const formData = new FormData()
        formData.set("commentId", commentId)
        formData.set("message", message)
        updateCommentFetcher.submit(formData, {
            method: "post",
            action: `/tickets/${ticketId}/update-comment`
        })
    }

    const handleEditToggle = () => {
        toggleEditMode();
    };

    const handleArchiveClick = (e: React.FormEvent) => {
        // If trying to unarchive a ticket and the project is archived, show warning
        if (ticket.isArchived && ticket.projectIsArchived) {
            e.preventDefault();
            setShowArchiveWarning(true);
            return;
        }
        // Otherwise, allow the form to submit normally
    };

    // TODO: Update permissions

    // TODO: Add confirm modal on delete

    if (error) {
        return <p className="text-error">{error}</p>;
    }

    const TicketDetails = (
        <>
            <div className="flex justify-between items-start">
                <div>
                    {ticket.isArchived &&
                        <div className="badge badge-warning font-medium mb-2">Archived</div>
                    }
                    <h1 className="text-3xl font-bold mb-2">{ticket.name}</h1>
                </div>
                <div className="flex gap-2 items-center">
                    <button
                        onClick={handleEditToggle}
                        className="btn btn-soft btn-sm"
                    >
                        <span className="material-symbols-outlined">edit</span>
                        Edit Details
                    </button>
                    <Form method="post" action={`/tickets/${ticketId}/archive`} onSubmit={handleArchiveClick}>
                        <input type="text" name="projectId" defaultValue={ticket.projectId} className="hidden" hidden aria-hidden />
                        <button
                            type="submit"
                            name="intent"
                            value={ticket.isArchived ? "unarchive" : "archive"}
                            className={`btn btn-sm ${ticket.isArchived ? 'btn-success' : 'btn-warning'}`}
                        >
                            <span className="material-symbols-outlined">folder</span>
                            {ticket.isArchived ? "Unarchive" : "Archive"}
                        </button>
                    </Form>
                    <Form method="post" action={`/tickets/${ticketId}/delete`}>
                        <input type="text" name="projectId" defaultValue={ticket.projectId} className="hidden" hidden aria-hidden />
                        <button
                            type="submit"
                            className="btn btn-sm btn-error"
                        >
                            <span className="material-symbols-outlined">delete</span>
                            Delete
                        </button>
                    </Form>
                </div>
            </div>

            {/* Developer Modal */}
            <DeveloperListModal
                modalRef={developersModalRef}
                members={(getDevelopersFetcher.data as JsonResponseResult<BasicUserInfo[]>)?.data}
                currentMember={ticket.assignee}
                actionFetcher={assignDeveloperFetcher}
                actionFetcherSubmit={(formData) => {
                    assignDeveloperFetcher.submit(formData, {
                        method: "post",
                        action: `/tickets/${ticketId}/update-dev`
                    })
                }}
            />


            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-4">
                <div className="stat bg-base-200 rounded-lg">
                    <div className="stat-title mb-2">Submitter</div>
                    {ticket.submitter && (
                        <div className="flex gap-2 items-center">
                            <div className="avatar">
                                <div className="w-9 rounded-full">

                                    <img src={ticket.submitter.avatarUrl} />
                                </div>
                            </div>
                            <div className="stat-value text-lg font-bold">
                                {ticket.submitter.name}
                            </div>
                        </div>
                    )}
                </div>
                <div className="stat bg-base-200 rounded-lg">
                    <div className="stat-title">Priority</div>
                    <div className="stat-value text-lg">
                        {updatePriorityFetcher.state === "submitting" ? (
                            <span className="loading loading-spinner"></span>
                        ) : (
                            <select
                                onChange={(e) => {
                                    const formData = new FormData();
                                    formData.append("priority", e.currentTarget.value)
                                    updatePriorityFetcher.submit(formData, {
                                        method: "post",
                                        action: `/tickets/${ticketId}/update-priority`,
                                    })
                                }}
                                className="bg-base-200 w-full"
                                value={ticket.priority}>
                                <option value="Low">🟢 Low</option>
                                <option value="Medium">🟡 Medium</option>
                                <option value="High">🟠 High</option>
                                <option value="Urgent">🔴 Urgent</option>
                            </select>
                        )}
                    </div>
                </div>
                <div className="stat bg-base-200 rounded-lg">
                    <div className="stat-title">Status</div>
                    <div className="stat-value text-lg">
                        {updateStatusFetcher.state === "submitting" ? (
                            <span className="loading loading-spinner"></span>
                        ) : (
                            <select
                                onChange={(e) => {
                                    const formData = new FormData();
                                    formData.append("status", e.target.value)
                                    updateStatusFetcher.submit(formData, {
                                        method: "post",
                                        action: `/tickets/${ticketId}/update-status`,
                                    })
                                }}
                                name="status"
                                className="bg-base-200 w-full"
                                value={ticket.status}>
                                <option value="New">🆕 New</option>
                                <option value="In Development">⚙️ In Development</option>
                                <option value="Testing">🧪 Testing</option>
                                <option value="Resolved">✅ Resolved</option>
                            </select>
                        )
                        }
                    </div>
                </div>
                <div className="stat bg-base-200 rounded-lg">
                    <div className="stat-title">Type</div>
                    <div className="stat-value text-lg font-bold">
                        {updateTypeFetcher.state === "submitting" ? (
                            <span className="loading loading-spinner"></span>
                        ) : (
                            <select
                                onChange={(e) => {
                                    const formData = new FormData();
                                    formData.append("type", e.target.value)
                                    updateTypeFetcher.submit(formData, {
                                        method: "post",
                                        action: `/tickets/${ticketId}/update-type`,
                                    })
                                }}
                                name="type"
                                className="bg-base-200 w-full"
                                value={ticket.type}>
                                <option value="Defect">🐛 Defect</option>
                                <option value="Feature">✨ Feature</option>
                                <option value="General Task">📋 General Task</option>
                                <option value="Change Request">🔄 Change Request</option>
                                <option value="Work Task">💼 Work Task</option>
                                <option value="Enhancement">⚡ Enhancement</option>
                            </select>
                        )
                        }
                    </div>
                </div>
            </div>

            <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                    <h2 className="text-lg font-bold">Developer</h2>
                    <div className="flex gap-2">
                        <button
                            onClick={() => handleOnGetDevelopers()}
                            className="btn btn-xs btn-soft"
                            title="Assign Developer"
                        >
                            <span className="material-symbols-outlined text-xs">person_add</span>
                        </button>
                        {ticket.assignee && (
                            <button
                                onClick={() => handleOnUnassignDeveloper()}
                                className="btn btn-xs btn-soft"
                                title="Unassign Developer"
                            >
                                <span className="material-symbols-outlined text-xs">person_remove</span>
                            </button>
                        )}
                    </div>
                </div>
                {ticket.assignee ? (
                    <div className="flex gap-2 items-center">
                        <div className="avatar">
                            <div className="w-9 rounded-full">
                                <img src={ticket.assignee.avatarUrl} />
                            </div>
                        </div>
                        <div className="font-bold">{ticket.assignee.name} </div>
                    </div>
                ) : (
                    <p className="font-medium text-gray-400">Unassigned</p>
                )}
            </div>

            <div className="flex flex-col mt-4">
                <p className="text-lg font-medium">Description:</p>
                <p className="">{ticket.description}</p>
            </div>
        </>
    )


    return (
        <RouteLayout>
            {ticket ? (
                <div className="flex flex-col gap-4">
                    <div className="flex justify-between items-center mb-2">
                        <BackButton />
                    </div>
                    <div className="bg-base-100 rounded-lg shadow-lg p-6">
                        {isEditing ? (
                            <EditModeForm
                                error={formError}
                                isSubmitting={navigation.state === "submitting"}
                                onCancel={handleEditToggle}
                            >
                                <div className="form-control mb-4">
                                    <label className="label">
                                        <span className="label-text">Ticket Name</span>
                                    </label>
                                    <input
                                        type="text"
                                        name="name"
                                        className="input input-bordered w-full"
                                        defaultValue={ticket.name}
                                        required
                                        maxLength={50}
                                    />
                                </div>

                                <div className="form-control mb-4">
                                    <label className="label">
                                        <span className="label-text">Description</span>
                                    </label>
                                    <textarea
                                        name="description"
                                        className="textarea textarea-bordered w-full"
                                        defaultValue={ticket.description}
                                        rows={4}
                                        required
                                        maxLength={1000}
                                    ></textarea>
                                </div>

                                <div className="flex gap-3">
                                    <div className="flex flex-col gap-1">
                                        <label className="label" htmlFor="priority">Description</label>
                                        <select
                                            name="priority"
                                            className="select w-max"
                                            defaultValue={ticket.priority}>
                                            <option value="Low">🟢 Low</option>
                                            <option value="Medium">🟡 Medium</option>
                                            <option value="High">🟠 High</option>
                                            <option value="Urgent">🔴 Urgent</option>
                                        </select>
                                    </div>
                                    <div className="flex flex-col gap-1">
                                        <label className="label" htmlFor="status">Status</label>
                                        <select
                                            name="status"
                                            className="select w-max"
                                            defaultValue={ticket.status}>
                                            <option value="New">🆕 New</option>
                                            <option value="In Development">⚙️ In Development</option>
                                            <option value="Testing">🧪 Testing</option>
                                            <option value="Resolved">✅ Resolved</option>
                                        </select>
                                    </div>
                                    <div className="flex flex-col gap-1">
                                        <label className="label" htmlFor="type">Type</label>
                                        <select
                                            name="type"
                                            className="select w-max"
                                            defaultValue={ticket.type}>
                                            <option value="Defect">🐛 Defect</option>
                                            <option value="Feature">✨ Feature</option>
                                            <option value="General Task">📋 General Task</option>
                                            <option value="Change Request">🔄 Change Request</option>
                                            <option value="Work Task">💼 Work Task</option>
                                            <option value="Enhancement">⚡ Enhancement</option>
                                        </select>
                                    </div>
                                </div>
                            </EditModeForm>
                        ) : (<>{TicketDetails}</>)}
                    </div>

                    {/* Comments and Attachments Side by Side */}
                    <div className="grid grid-cols-1 2xl:grid-cols-2 gap-4">
                        <div className="bg-base-100 rounded-lg shadow-lg p-6">
                            <h2 className="text-xl font-bold mb-4">Comments</h2>
                            <div className="max-w-full">
                                <ChatBox
                                    className="p-4 flex flex-col col-reverse w-full max-h-[450px] overflow-y-auto"
                                    onDeleteComment={handleOnDeleteComment}
                                    onEditComment={handleOnEditComment}
                                    comments={(getCommentsFetcher.data as any)?.data}
                                    loading={getCommentsFetcher.state === "loading"}
                                    userId={userInfo.memberId} />

                                <Form
                                    className="mt-4"
                                    method="post"
                                    navigate={false}
                                    fetcherKey="create-comment"
                                    ref={commentsFormRef}
                                    action={`/tickets/${ticketId}/create-comment`}>
                                    <div className="flex gap-2">
                                        <button type="submit" className="btn btn-primary">Send</button>
                                        <textarea
                                            placeholder="Message"
                                            name="message"
                                            className="textarea w-full resize-none field-sizing-content min-h-auto" />
                                    </div>
                                </Form>
                            </div>
                        </div>

                        <div className="bg-base-100 rounded-lg shadow-lg p-6">
                            <h2 className="text-xl font-bold mb-4">Attachments</h2>
                            <AttachmentSection
                                ticketId={ticketId!}
                                userInfo={userInfo}
                                ticket={ticket}
                            />
                        </div>
                    </div>

                    <div className="bg-base-100 rounded-lg shadow-lg p-6">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-xl font-bold">History</h2>
                            <button
                                onClick={() => getHistoryFetcher.load(`/tickets/${ticketId}/get-history`)}
                                disabled={getHistoryFetcher.state === "loading"}
                                className="btn btn-sm btn-ghost btn-circle"
                                title="Refresh history"
                            >
                                {getHistoryFetcher.state === "loading" ? (
                                    <span className="loading loading-spinner loading-sm"></span>
                                ) : (
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                )}
                            </button>
                        </div>
                        <div className="w-2xl">
                            <TicketTimeline
                                history={(getHistoryFetcher.data as any)?.data || []}
                                loading={getHistoryFetcher.state === "loading"}
                            />
                        </div>
                    </div>
                </div>
            ) : (
                <div className="flex justify-center items-center h-full">
                    <p>Loading ticket details...</p>
                </div>
            )}

            {/* Archive Warning Modal */}
            <ArchiveWarningModal
                isOpen={showArchiveWarning}
                onClose={() => setShowArchiveWarning(false)}
                title="Cannot Unarchive Ticket"
                message="Cannot unarchive this ticket because its project is archived. Please unarchive the project first before unarchiving individual tickets."
            />
        </RouteLayout>
    );
}

export async function action({ request, params }: ActionFunctionArgs) {
    const session = await getSession(request);
    const userRole = session.get("user").role;

    if (!validateRole(userRole, permissions.ticket.create)) {
        return ForbiddenResponse();
    }
    const { data: tokenResponse, error: tokenError } = await tryCatch(
        apiClient.auth.getValidToken(session),
    );

    if (tokenError) {
        return redirect("/logout");
    }

    const ticketId = params.ticketId!;
    const formData = await request.formData();
    const name = formData.get("name") as string;
    const description = formData.get("description") as string;
    const priority = formData.get("priority") as string;
    const status = formData.get("status") as string;
    const type = formData.get("type") as string;

    const ticketData = {
        name,
        description,
        priority,
        status,
        type
    };

    const { error } = await tryCatch(
        updateTicket(ticketId, ticketData, tokenResponse.token),
    );

    if (error instanceof AuthenticationError) {
        return redirect("/logout");
    }

    if (error) {
        return ActionResponse({
            success: false,
            error: error.message,
        });
    }

    return ActionResponse({
        success: true,
        error: null,
    });
}


